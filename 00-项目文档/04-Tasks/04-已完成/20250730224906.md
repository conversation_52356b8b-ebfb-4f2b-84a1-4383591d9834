# TODO

1. 接下来，我需要为这个 iOS 前端部分的所有业务功能提取业务逻辑地图。
2. 阅读提示词：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/03-Docs/01-提示词/代码审阅/002-业务逻辑地图.md
3. 预备在 /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/03-Docs/06-业务逻辑地图 创建多个独立的业务逻辑地图文件
4. 先提供你对前端的业务逻辑划分

# TODO 2

1. 阅读提示词：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/03-Docs/01-提示词/代码审阅/002-业务逻辑地图.md
2. 在 /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/03-Docs/06-业务逻辑地图
3. 梳理业务需求：01-搜索与发现业务逻辑地图.md

## AI 分析

### 代码架构分析

基于对 iOS 前端代码的深入分析，我发现了一个结构清晰、职责分明的 SwiftUI 应用架构：

**核心架构特点：**

- 采用 MVVM + Coordinator 模式
- 依赖注入容器管理服务生命周期
- 分层清晰：UI 层、ViewModel 层、Service 层、Network 层、Data 层
- 状态管理使用@Published、@StateObject、@ObservedObject
- 支持离线优先的搜索体验（LPLC 原则）

**关键发现：**

1. **搜索功能**是核心业务，采用本地索引+网络 API 的混合策略
2. **音频管理**有完整的 TTS 集成和缓存机制
3. **收藏系统**支持本地存储和云端同步
4. **设置管理**包含用户偏好、语言配置、订阅状态
5. **推荐系统**提供无限内容流体验
6. **水平舞台**提供沉浸式单词学习体验

### iOS 前端业务逻辑划分建议

根据代码分析，我建议将 iOS 前端业务逻辑划分为以下 8 个核心模块：

#### 1. 搜索与发现业务逻辑

**核心功能：** 用户搜索单词、获取搜索建议、查看搜索结果
**关键组件：**

- SearchView + SearchViewModel
- SearchService (本地索引 + 网络 API)
- LocalIndexService (离线搜索)
- SearchAPIAdapter (搜索 API 适配)
- SearchCoordinator (搜索状态协调)

#### 2. 单词详情与学习体验业务逻辑

**核心功能：** 单词详情展示、水平舞台交互、深度学习体验
**关键组件：**

- WordResultView
- HorizontalStageContainer + HorizontalStageViewModel
- DeepContextComponent (释义、想象、词源)
- ExampleStageComponent (例句展示)
- ScenarioCarouselComponent (使用场景)
- WordAPIAdapter (单词内容 API)

#### 3. 音频播放业务逻辑

**核心功能：** 音频播放、音频缓存管理
**关键组件：**

- GlobalAudioManager (全局音频管理)
- AutoAudioManager (自动播放)
- AudioStatusIndicator (音频状态显示)
- JITPreloader (即时预加载)

#### 4. 收藏与书签业务逻辑

**核心功能：** 单词收藏、收藏列表管理、云端同步
**关键组件：**

- LocalBookmarkService + LocalBookmarkViewModel
- CloudSyncService (云端同步)
- FloatingBookmarkButton (收藏按钮)
- SQLiteManager (本地存储)

#### 5. 推荐与内容流业务逻辑

**核心功能：** 推荐算法、无限内容流、个性化推荐
**关键组件：**

- RecommendationArrayManager
- 推荐策略算法
- 内容预加载机制

#### 6. 用户设置与配置业务逻辑

**核心功能：** 用户偏好设置、语言配置、应用设置
**关键组件：**

- SettingsView + SettingsViewModel
- SettingsService + GlobalSettingsManager
- LanguageSelectionView (语言选择)
- PhoneticPreferenceView (音标偏好)
- LanguagePairConfigManager (语言对配置)

#### 7. 订阅与权益业务逻辑

**核心功能：** 订阅管理、权益验证、匿名购买
**关键组件：**

- SubscriptionView + SubscriptionViewModel
- AnonymousPurchaseService (匿名购买)
- 权益验证逻辑

#### 8. 应用生命周期与基础设施业务逻辑

**核心功能：** 应用启动、依赖注入、性能监控、数据同步
**关键组件：**

- SenseWordMainView (应用入口)
- MainContentView (主界面容器)
- DIContainer (依赖注入)
- PerformanceMonitor (性能监控)
- IndexDownloadManager (索引下载)

### 业务逻辑地图文件规划

建议创建以下业务逻辑地图文件：

1. `01-搜索与发现业务逻辑地图.md`
2. `02-单词详情与学习体验业务逻辑地图.md`
3. `03-音频播放逻辑地图.md`
4. `04-收藏与书签业务逻辑地图.md`
5. `05-推荐与内容流业务逻辑地图.md`
6. `06-用户设置与配置业务逻辑地图.md`
7. `07-订阅与权益业务逻辑地图.md`
8. `08-应用生命周期与基础设施业务逻辑地图.md`
